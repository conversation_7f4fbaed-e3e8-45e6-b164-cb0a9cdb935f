# <PERSON><PERSON> thống Phân quyền RBAC (Role-Based Access Control)

## Tổng quan

Hệ thống phân quyền RBAC được thiết kế để quản lý quyền truy cập một cách linh hoạt và bảo mật. <PERSON><PERSON> thống bao gồm:

- **User**: <PERSON><PERSON><PERSON><PERSON> dùng của hệ thống
- **Role**: <PERSON><PERSON> trò của người dùng (Admin, Editor, Viewer, ...)
- **Permission**: <PERSON>uy<PERSON><PERSON> cụ thể cho từng thao tác
- **Permission Group**: <PERSON><PERSON><PERSON><PERSON> các quyền theo module

## Cấu trúc Database

### 1. Users Table

```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(255) NOT NULL,
  password VARCHAR(255) NOT NULL,
  fullName VARCHAR(255),
  isActive BOOLEAN DEFAULT TRUE,
  lastLoginAt TIMESTAMP,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deletedAt TIMESTAMP NULL,
  createdBy VARCHAR(255),
  updatedBy VARCHAR(255),
  deletedBy VARCHAR(255),
  metadata JSON
);
```

### 2. Roles Table

```sql
CREATE TABLE roles (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  description VARCHAR(255),
  isActive BOOLEAN DEFAULT TRUE,
  isSystem BOOLEAN DEFAULT FALSE,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deletedAt TIMESTAMP NULL,
  createdBy VARCHAR(255),
  updatedBy VARCHAR(255),
  deletedBy VARCHAR(255),
  metadata JSON
);
```

### 3. Permission Groups Table

```sql
CREATE TABLE permission_groups (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  description VARCHAR(255),
  module VARCHAR(50) NOT NULL,
  isActive BOOLEAN DEFAULT TRUE,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deletedAt TIMESTAMP NULL,
  createdBy VARCHAR(255),
  updatedBy VARCHAR(255),
  deletedBy VARCHAR(255),
  metadata JSON
);
```

### 4. Permissions Table

```sql
CREATE TABLE permissions (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  description VARCHAR(255),
  module VARCHAR(50) NOT NULL,
  resource VARCHAR(50) NOT NULL,
  action VARCHAR(20) NOT NULL,
  isActive BOOLEAN DEFAULT TRUE,
  groupId VARCHAR(36) NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deletedAt TIMESTAMP NULL,
  createdBy VARCHAR(255),
  updatedBy VARCHAR(255),
  deletedBy VARCHAR(255),
  metadata JSON,
  FOREIGN KEY (groupId) REFERENCES permission_groups(id)
);
```

### 5. Junction Tables

```sql
-- User-Role relationship
CREATE TABLE user_roles (
  user_id VARCHAR(36) NOT NULL,
  role_id VARCHAR(36) NOT NULL,
  PRIMARY KEY (user_id, role_id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Role-Permission relationship
CREATE TABLE role_permissions (
  role_id VARCHAR(36) NOT NULL,
  permission_id VARCHAR(36) NOT NULL,
  PRIMARY KEY (role_id, permission_id),
  FOREIGN KEY (role_id) REFERENCES roles(id),
  FOREIGN KEY (permission_id) REFERENCES permissions(id)
);
```

## API Endpoints

### Authentication

- `POST /auth/login` - Đăng nhập
- `POST /auth/register` - Đăng ký

### Roles Management

- `POST /roles` - Tạo role mới
- `GET /roles` - Lấy danh sách roles
- `GET /roles/:id` - Lấy thông tin role
- `PUT /roles/:id` - Cập nhật role
- `DELETE /roles/:id` - Xóa role
- `POST /roles/:id/permissions` - Gán permissions cho role
- `GET /roles/:id/permissions` - Lấy permissions của role

### Permission Groups Management

- `POST /permission-groups` - Tạo permission group mới
- `GET /permission-groups` - Lấy danh sách permission groups
- `GET /permission-groups/module/:module` - Lấy permission groups theo module
- `GET /permission-groups/:id` - Lấy thông tin permission group
- `PUT /permission-groups/:id` - Cập nhật permission group
- `DELETE /permission-groups/:id` - Xóa permission group

### Permissions Management

- `POST /permissions` - Tạo permission mới
- `GET /permissions` - Lấy danh sách permissions
- `GET /permissions/group/:groupId` - Lấy permissions theo group
- `GET /permissions/module/:module` - Lấy permissions theo module
- `GET /permissions/resource/:resource` - Lấy permissions theo resource
- `GET /permissions/:id` - Lấy thông tin permission
- `PUT /permissions/:id` - Cập nhật permission
- `DELETE /permissions/:id` - Xóa permission

### Users Management

- `POST /users` - Tạo user mới
- `GET /users` - Lấy danh sách users
- `GET /users/:id` - Lấy thông tin user
- `PUT /users/:id` - Cập nhật user
- `DELETE /users/:id` - Xóa user
- `POST /users/:id/roles` - Gán roles cho user
- `GET /users/:id/roles` - Lấy roles của user

## Cách sử dụng

### 1. Khởi tạo dữ liệu mẫu

```bash
npm run seed
```

### 2. Đăng nhập với tài khoản admin

```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

### 3. Tạo Permission Group

```bash
curl -X POST http://localhost:3000/permission-groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Content Management",
    "description": "Permissions for content management",
    "module": "cms"
  }'
```

### 4. Tạo Permission

```bash
curl -X POST http://localhost:3000/permissions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "cms.content.create",
    "description": "Permission to create content",
    "module": "cms",
    "resource": "content",
    "action": "create",
    "groupId": "PERMISSION_GROUP_ID"
  }'
```

### 5. Tạo Role

```bash
curl -X POST http://localhost:3000/roles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Content Editor",
    "description": "Role for content editing",
    "permissionIds": ["PERMISSION_ID_1", "PERMISSION_ID_2"]
  }'
```

### 6. Gán Role cho User

```bash
curl -X POST http://localhost:3000/users/USER_ID/roles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "roleIds": ["ROLE_ID_1", "ROLE_ID_2"]
  }'
```

## Sử dụng trong Code

### 1. Bảo vệ endpoint với permission

```typescript
import { RequirePermissions } from '../common/decorators/permissions.decorator';

@Controller('media')
export class MediaController {
  @Get()
  @RequirePermissions('cms.media.read')
  findAll() {
    return this.mediaService.findAll();
  }

  @Post()
  @RequirePermissions('cms.media.create')
  create(@Body() createMediaDto: CreateMediaDto) {
    return this.mediaService.create(createMediaDto);
  }
}
```

### 2. Kiểm tra permission trong service

```typescript
import { AuthService } from '../modules/auth/auth.service';

@Injectable()
export class MediaService {
  constructor(private authService: AuthService) {}

  async createMedia(userId: string, data: any) {
    // Kiểm tra quyền
    const hasPermission = await this.authService.hasPermission(
      userId,
      'cms.media.create',
    );

    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions');
    }

    // Thực hiện tạo media
    return this.mediaRepository.save(data);
  }
}
```

### 3. Lấy thông tin user hiện tại

```typescript
import { CurrentUser } from '../common/decorators/current-user.decorator';

@Controller('profile')
export class ProfileController {
  @Get()
  getProfile(@CurrentUser() user: any) {
    return user;
  }
}
```

## Luồng phân quyền

1. **User đăng nhập** → JWT token được tạo với thông tin user và roles
2. **Request đến API** → JWT token được verify và user info được attach vào request
3. **Permission Guard** → Kiểm tra permission của user với endpoint
4. **Service logic** → Thực hiện business logic nếu có quyền

## Best Practices

1. **Naming Convention**: Sử dụng format `module.resource.action` cho permission names
2. **Granular Permissions**: Tạo permission chi tiết cho từng thao tác
3. **Role Hierarchy**: Sử dụng role để nhóm permissions thay vì gán trực tiếp
4. **Audit Trail**: Log tất cả các thay đổi permission và role
5. **Default Roles**: Tạo sẵn các role cơ bản (Admin, Editor, Viewer)
6. **System Roles**: Bảo vệ các role hệ thống khỏi bị xóa/sửa

## Security Considerations

1. **JWT Secret**: Sử dụng secret key mạnh và thay đổi định kỳ
2. **Password Hashing**: Luôn hash password trước khi lưu
3. **Input Validation**: Validate tất cả input từ user
4. **SQL Injection**: Sử dụng TypeORM để tránh SQL injection
5. **Rate Limiting**: Giới hạn số request để tránh brute force
6. **HTTPS**: Sử dụng HTTPS trong production
