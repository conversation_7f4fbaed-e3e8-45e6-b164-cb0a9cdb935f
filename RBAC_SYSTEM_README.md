# RBAC System Implementation

This document describes the complete Role-Based Access Control (RBAC) system implementation for the CMS Backend.

## System Overview

The RBAC system includes the following entities:
- **Users**: System users with authentication credentials
- **Departments**: Organizational departments
- **Teams**: Teams within departments
- **Roles**: User roles with different permission levels
- **Permissions**: Granular permissions for system actions
- **Permission Groups**: Logical grouping of permissions
- **Role-Permission Assignments**: Mapping roles to permissions with department/team context
- **User-Role Assignments**: Mapping users to roles with department/team context

## Database Schema

The system follows the ER diagram provided with the following key relationships:
- Users can have multiple roles in different departments/teams
- Roles can have different permissions in different departments/teams
- Permissions can be grouped for easier management
- All assignments support department and team context for granular control

## API Endpoints

### Users Management
- `GET /api/v1/users` - List users with pagination and search
- `GET /api/v1/users/:id` - Get user by ID
- `POST /api/v1/users` - Create new user
- `PATCH /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user (soft delete)

### Departments Management
- `GET /api/v1/departments` - List departments
- `GET /api/v1/departments/:id` - Get department by ID
- `GET /api/v1/departments/:id/stats` - Get department statistics
- `POST /api/v1/departments` - Create new department
- `PATCH /api/v1/departments/:id` - Update department
- `DELETE /api/v1/departments/:id` - Delete department

### Teams Management
- `GET /api/v1/teams` - List teams
- `GET /api/v1/teams/:id` - Get team by ID
- `GET /api/v1/teams/by-department/:departmentId` - Get teams by department
- `GET /api/v1/teams/:id/stats` - Get team statistics
- `POST /api/v1/teams` - Create new team
- `PATCH /api/v1/teams/:id` - Update team
- `DELETE /api/v1/teams/:id` - Delete team

### Permissions Management
- `GET /api/v1/permissions` - List permissions
- `GET /api/v1/permissions/:id` - Get permission by ID

### Roles Management
- `GET /api/v1/roles` - List roles
- `GET /api/v1/roles/:id` - Get role by ID
- `GET /api/v1/roles/system` - Get system roles
- `GET /api/v1/roles/non-system` - Get non-system roles

### Permission Groups Management
- `GET /api/v1/permission-groups` - List permission groups
- `GET /api/v1/permission-groups/:id` - Get permission group by ID
- `GET /api/v1/permission-groups/:id/permissions` - Get permissions in group
- `POST /api/v1/permission-groups` - Create permission group
- `POST /api/v1/permission-groups/:id/permissions` - Assign permissions to group
- `PATCH /api/v1/permission-groups/:id` - Update permission group
- `DELETE /api/v1/permission-groups/:id` - Delete permission group
- `DELETE /api/v1/permission-groups/:id/permissions` - Remove permissions from group

### Role-Permission Assignments
- `GET /api/v1/role-permission-assignments` - List assignments with filters
- `GET /api/v1/role-permission-assignments/:id` - Get assignment by ID
- `GET /api/v1/role-permission-assignments/roles/:roleId/permissions` - Get role permissions
- `POST /api/v1/role-permission-assignments` - Create assignment
- `POST /api/v1/role-permission-assignments/bulk-assign` - Bulk assign permissions to role
- `PATCH /api/v1/role-permission-assignments/:id` - Update assignment
- `DELETE /api/v1/role-permission-assignments/:id` - Delete assignment
- `DELETE /api/v1/role-permission-assignments/roles/:roleId/permissions` - Remove permissions from role

### User-Role Assignments
- `GET /api/v1/user-role-assignments` - List assignments with filters
- `GET /api/v1/user-role-assignments/:id` - Get assignment by ID
- `GET /api/v1/user-role-assignments/users/:userId/roles` - Get user roles
- `GET /api/v1/user-role-assignments/roles/:roleId/users` - Get users with role
- `POST /api/v1/user-role-assignments` - Create assignment
- `POST /api/v1/user-role-assignments/bulk-assign` - Bulk assign roles to user
- `PATCH /api/v1/user-role-assignments/:id` - Update assignment
- `DELETE /api/v1/user-role-assignments/:id` - Delete assignment
- `DELETE /api/v1/user-role-assignments/users/:userId/roles` - Remove roles from user

## Setup Instructions

### 1. Database Setup
Make sure your MySQL database is running and configured in your `.env` file:

```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=cms_db
DB_SYNCHRONIZE=true
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Run Database Migrations
The system uses TypeORM with `synchronize: true` for development, so tables will be created automatically.

### 4. Seed Initial Data
Run the seeder to populate permissions, roles, and create the super admin user:

```bash
npm run seed
```

This will create:
- 8 basic permissions (create, update, delete, import, export, view, print, analytics)
- 4 roles (Super Admin, Manager Department, Team Leader, Team Member)
- 1 super admin user (username: super_admin, email: <EMAIL>, password: aqSj0isBIqKIbkR)

### 5. Start the Application
```bash
npm run start:dev
```

### 6. Access API Documentation
Visit `http://localhost:3000/api-docs` to view the Swagger API documentation.

## Usage Examples

### Creating a Department
```bash
curl -X POST http://localhost:3000/api/v1/departments \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Engineering"
  }'
```

### Creating a Team
```bash
curl -X POST http://localhost:3000/api/v1/teams \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Backend Team",
    "departmentId": "DEPARTMENT_ID"
  }'
```

### Creating a User
```bash
curl -X POST http://localhost:3000/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### Assigning Role to User
```bash
curl -X POST http://localhost:3000/api/v1/user-role-assignments \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "USER_ID",
    "roleId": "ROLE_ID",
    "departmentId": "DEPARTMENT_ID",
    "teamId": "TEAM_ID"
  }'
```

### Bulk Assigning Permissions to Role
```bash
curl -X POST http://localhost:3000/api/v1/role-permission-assignments/bulk-assign \
  -H "Content-Type: application/json" \
  -d '{
    "roleId": "ROLE_ID",
    "permissionIds": ["PERMISSION_ID_1", "PERMISSION_ID_2"],
    "departmentId": "DEPARTMENT_ID"
  }'
```

## Key Features

1. **Hierarchical Permissions**: Permissions can be assigned at global, department, or team level
2. **Flexible Role System**: Roles can have different permissions in different contexts
3. **Soft Deletes**: All entities support soft deletion for data integrity
4. **Audit Trail**: All entities track who created, updated, or deleted records
5. **Search and Pagination**: All list endpoints support search and pagination
6. **Bulk Operations**: Support for bulk assignment of roles and permissions
7. **Validation**: Comprehensive input validation using class-validator
8. **API Documentation**: Complete Swagger documentation for all endpoints

## Security Considerations

1. Passwords are hashed using bcrypt with 12 rounds
2. All UUIDs are validated for proper format
3. Foreign key relationships are validated before creation
4. Soft deletes prevent accidental data loss
5. System roles are protected from deletion

## Testing

To test the system:

1. Run the seeder to create initial data
2. Use the Swagger UI at `/api-docs` to test endpoints
3. Create departments, teams, and users
4. Assign roles and permissions
5. Test the hierarchical permission system

The system is now ready for use with a complete RBAC implementation supporting granular permissions with department and team context.
