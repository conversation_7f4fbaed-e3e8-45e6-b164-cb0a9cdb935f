# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
NODE_ENV=development
PORT=3000
APP_NAME=CMS Backend
APP_VERSION=1.0.0
APP_DESCRIPTION=Content Management System with RBAC

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_database_password
DB_DATABASE=cms_db
DB_SYNCHRONIZE=true
DB_LOGGING=true
DB_MAX_CONNECTIONS=100
DB_SSL=false

# ===========================================
# JWT CONFIGURATION
# ===========================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=7d

# ===========================================
# CORS CONFIGURATION
# ===========================================
CORS_ORIGIN=http://localhost:5173,http://localhost:3001
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_CREDENTIALS=true

# ===========================================
# RATE LIMITING
# ===========================================
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# ===========================================
# LOGGING CONFIGURATION
# ===========================================
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true
LOG_MAX_FILES=5
LOG_MAX_SIZE=10m

# ===========================================
# SWAGGER CONFIGURATION
# ===========================================
SWAGGER_ENABLED=true
SWAGGER_PATH=api-docs
SWAGGER_TITLE=CMS API Documentation
SWAGGER_DESCRIPTION=API documentation for Content Management System
SWAGGER_VERSION=1.0.0

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret
HELMET_ENABLED=true

# ===========================================
# FILE UPLOAD CONFIGURATION
# ===========================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_DEST=./uploads

# ===========================================
# EMAIL CONFIGURATION (Optional)
# ===========================================
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_email_password
MAIL_FROM=<EMAIL>

# ===========================================
# REDIS CONFIGURATION (Optional)
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
