### Test Authentication API

### 1. Register a new user
POST http://localhost:3000/api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+1234567890",
  "address": "123 Test St",
  "city": "Test City",
  "state": "TS",
  "zip": "12345",
  "country": "Test Country"
}

### 2. Login
POST http://localhost:3000/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 3. Get user profile (requires authentication)
GET http://localhost:3000/api/v1/auth/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 4. Refresh token
GET http://localhost:3000/api/v1/auth/refresh
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 5. Get all users (requires authentication)
GET http://localhost:3000/api/v1/users
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 6. Get user by ID (requires authentication)
GET http://localhost:3000/api/v1/users/USER_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 7. Create new user (requires admin/manager role)
POST http://localhost:3000/api/v1/users
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "username": "newuser",
  "fullName": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+1234567890"
}

### 8. Update user (requires admin/manager role)
PATCH http://localhost:3000/api/v1/users/USER_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "fullName": "Updated User Name",
  "phone": "+9876543210"
}

### 9. Delete user (requires admin role)
DELETE http://localhost:3000/api/v1/users/USER_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE 