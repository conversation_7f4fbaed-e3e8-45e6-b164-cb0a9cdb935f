import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { PermissionSeeder } from './permission.seeder';
import { RoleSeeder } from './role.seeder';
import { UserSeeder } from './user.seeder';

// Import all entities
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Team } from '../../entities/team.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { PermissionGroup } from '../../entities/permission-group.entity';
import { PermissionGroupDetail } from '../../entities/permission-group-detail.entity';
import { RolePermissionAssignment } from '../../entities/role-permission-assignment.entity';
import { UserRoleAssignment } from '../../entities/user-role-assignment.entity';

/**
 * Main Seeder Runner
 * Runs all seeders in the correct order
 */
async function runSeeders() {
  console.log('Starting database seeding...');

  // Create DataSource configuration
  const dataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'cms_db',
    entities: [
      User,
      Department,
      Team,
      Role,
      Permission,
      PermissionGroup,
      PermissionGroupDetail,
      RolePermissionAssignment,
      UserRoleAssignment,
    ],
    synchronize: false,
    logging: false,
  });

  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection established');

    // Run seeders in order
    await PermissionSeeder.run(dataSource);
    await RoleSeeder.run(dataSource);
    await UserSeeder.run(dataSource);

    console.log('All seeders completed successfully!');
  } catch (error) {
    console.error('Error running seeders:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await dataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the seeders
runSeeders();
