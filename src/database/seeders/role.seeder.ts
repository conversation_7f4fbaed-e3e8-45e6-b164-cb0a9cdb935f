import { DataSource } from 'typeorm';
import { Role } from '../../entities/role.entity';

/**
 * Role Seeder
 * Seeds the roles table with predefined roles
 */
export class RoleSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const roleRepository = dataSource.getRepository(Role);

    // Check if roles already exist
    const existingRoles = await roleRepository.count();
    if (existingRoles > 0) {
      console.log('Roles already exist, skipping seeder...');
      return;
    }

    const roles = [
      {
        displayName: 'Super Admin',
        name: 'super_admin',
        level: 1,
        description: '<PERSON><PERSON><PERSON> quyền hệ thống',
        isSystem: true,
      },
      {
        displayName: 'Manager <PERSON><PERSON> phận',
        name: 'manager_department',
        level: 2,
        description: 'Quản lý 1 bộ phận',
        isSystem: false,
      },
      {
        displayName: 'Team Leader',
        name: 'team_leader',
        level: 3,
        description: 'Trưởng nhóm',
        isSystem: false,
      },
      {
        displayName: 'Team Member',
        name: 'team_member',
        level: 4,
        description: 'Thành viên nhóm',
        isSystem: false,
      },
    ];

    console.log('Seeding roles...');
    
    for (const roleData of roles) {
      const role = roleRepository.create({
        displayName: roleData.displayName,
        name: roleData.name,
        level: roleData.level,
        description: roleData.description,
        isSystem: roleData.isSystem,
        createdBy: 'system',
      });

      await roleRepository.save(role);
      console.log(`Created role: ${role.displayName} (${role.name})`);
    }

    console.log('Role seeding completed!');
  }
}
