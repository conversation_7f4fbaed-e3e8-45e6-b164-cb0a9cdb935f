import { DataSource } from 'typeorm';
import { Permission } from '../../entities/permission.entity';

/**
 * Permission Seeder
 * Seeds the permissions table with predefined permissions
 */
export class PermissionSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const permissionRepository = dataSource.getRepository(Permission);

    // Check if permissions already exist
    const existingPermissions = await permissionRepository.count();
    if (existingPermissions > 0) {
      console.log('Permissions already exist, skipping seeder...');
      return;
    }

    const permissions = [
      {
        action: 'create',
        description: 'Tạo mới',
      },
      {
        action: 'update',
        description: 'Cập nhật',
      },
      {
        action: 'delete',
        description: 'Xóa',
      },
      {
        action: 'import',
        description: 'Nhập dữ liệu',
      },
      {
        action: 'export',
        description: 'Xuất dữ liệu',
      },
      {
        action: 'view',
        description: 'Xem',
      },
      {
        action: 'print',
        description: 'In',
      },
      {
        action: 'analytics',
        description: 'Phân tích',
      },
    ];

    console.log('Seeding permissions...');
    
    for (const permissionData of permissions) {
      const permission = permissionRepository.create({
        action: permissionData.action,
        description: permissionData.description,
        createdBy: 'system',
      });

      await permissionRepository.save(permission);
      console.log(`Created permission: ${permission.action}`);
    }

    console.log('Permission seeding completed!');
  }
}
