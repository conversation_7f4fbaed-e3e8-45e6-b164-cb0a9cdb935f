import { DataSource } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from '../../entities/user.entity';

/**
 * User Seeder
 * Seeds the users table with predefined users
 */
export class UserSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const userRepository = dataSource.getRepository(User);

    // Check if users already exist
    const existingUsers = await userRepository.count();
    if (existingUsers > 0) {
      console.log('Users already exist, skipping seeder...');
      return;
    }

    const users = [
      {
        username: 'super_admin',
        fullName: 'Super Admin',
        email: '<EMAIL>',
        password: 'aqSj0isBIqKIbkR',
      },
    ];

    console.log('Seeding users...');
    
    for (const userData of users) {
      // Hash the password
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      
      const user = userRepository.create({
        username: userData.username,
        fullName: userData.fullName,
        email: userData.email,
        password: hashedPassword,
        createdBy: 'system',
      });

      await userRepository.save(user);
      console.log(`Created user: ${user.username} (${user.email})`);
    }

    console.log('User seeding completed!');
  }
}
