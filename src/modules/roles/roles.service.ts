import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from '../../entities/role.entity';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '../../common/dto/pagination.dto';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Role>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.roleRepository.createQueryBuilder('role');
    
    // Add search functionality
    if (search) {
      queryBuilder.where(
        '(role.name LIKE :search OR role.displayName LIKE :search OR role.description LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Add sorting
    const sortField = sortBy || 'level';
    const sortDirection = sortOrder || 'ASC';
    queryBuilder.orderBy(`role.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [roles, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(roles, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['userRoles', 'rolePermissions'],
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    return role;
  }

  async findByName(name: string): Promise<Role | null> {
    return await this.roleRepository.findOne({
      where: { name },
    });
  }

  async findSystemRoles(): Promise<Role[]> {
    return await this.roleRepository.find({
      where: { isSystem: true },
      order: { level: 'ASC' },
    });
  }

  async findNonSystemRoles(): Promise<Role[]> {
    return await this.roleRepository.find({
      where: { isSystem: false },
      order: { level: 'ASC' },
    });
  }
}
