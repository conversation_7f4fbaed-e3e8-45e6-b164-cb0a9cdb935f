import {
  <PERSON>,
  Get,
  Param,
  Query,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { RolesService } from './roles.service';
import { PaginationDto, PaginatedResult } from '../../common/dto/pagination.dto';
import { Role } from '../../entities/role.entity';

@ApiTags('Roles')
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all roles with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Roles retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<Role>> {
    return await this.rolesService.findAll(paginationDto);
  }

  @Get('system')
  @ApiOperation({ summary: 'Get system roles' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'System roles retrieved successfully',
  })
  async findSystemRoles(): Promise<Role[]> {
    return await this.rolesService.findSystemRoles();
  }

  @Get('non-system')
  @ApiOperation({ summary: 'Get non-system roles' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Non-system roles retrieved successfully',
  })
  async findNonSystemRoles(): Promise<Role[]> {
    return await this.rolesService.findNonSystemRoles();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Role retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Role not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Role> {
    return await this.rolesService.findOne(id);
  }
}
