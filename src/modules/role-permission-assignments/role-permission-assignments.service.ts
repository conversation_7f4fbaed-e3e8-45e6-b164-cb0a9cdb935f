import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { RolePermissionAssignment } from '../../entities/role-permission-assignment.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { Department } from '../../entities/department.entity';
import { Team } from '../../entities/team.entity';
import { CreateRolePermissionAssignmentDto } from './dto/create-role-permission-assignment.dto';
import { UpdateRolePermissionAssignmentDto } from './dto/update-role-permission-assignment.dto';
import { BulkAssignPermissionsDto } from './dto/bulk-assign-permissions.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '../../common/dto/pagination.dto';

@Injectable()
export class RolePermissionAssignmentsService {
  constructor(
    @InjectRepository(RolePermissionAssignment)
    private readonly rolePermissionAssignmentRepository: Repository<RolePermissionAssignment>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
  ) {}

  async create(createDto: CreateRolePermissionAssignmentDto, createdBy?: string): Promise<RolePermissionAssignment> {
    // Validate role exists
    const role = await this.roleRepository.findOne({ where: { id: createDto.roleId } });
    if (!role) {
      throw new BadRequestException('Role not found');
    }

    // Validate permission exists
    const permission = await this.permissionRepository.findOne({ where: { id: createDto.permissionId } });
    if (!permission) {
      throw new BadRequestException('Permission not found');
    }

    // Validate department if provided
    if (createDto.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: createDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team if provided
    if (createDto.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: createDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }
    }

    // Check if assignment already exists
    const existingAssignment = await this.rolePermissionAssignmentRepository.findOne({
      where: {
        roleId: createDto.roleId,
        permissionId: createDto.permissionId,
        departmentId: createDto.departmentId || null,
        teamId: createDto.teamId || null,
      },
    });
    if (existingAssignment) {
      throw new ConflictException('Role-permission assignment already exists for this context');
    }

    // Create assignment
    const assignment = this.rolePermissionAssignmentRepository.create({
      ...createDto,
      createdBy,
    });

    return await this.rolePermissionAssignmentRepository.save(assignment);
  }

  async bulkAssign(bulkAssignDto: BulkAssignPermissionsDto, createdBy?: string): Promise<RolePermissionAssignment[]> {
    // Validate role exists
    const role = await this.roleRepository.findOne({ where: { id: bulkAssignDto.roleId } });
    if (!role) {
      throw new BadRequestException('Role not found');
    }

    // Validate permissions exist
    const permissions = await this.permissionRepository.find({
      where: { id: In(bulkAssignDto.permissionIds) },
    });
    if (permissions.length !== bulkAssignDto.permissionIds.length) {
      throw new BadRequestException('One or more permissions not found');
    }

    // Validate department if provided
    if (bulkAssignDto.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: bulkAssignDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team if provided
    if (bulkAssignDto.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: bulkAssignDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }
    }

    // Check for existing assignments
    const existingAssignments = await this.rolePermissionAssignmentRepository.find({
      where: {
        roleId: bulkAssignDto.roleId,
        permissionId: In(bulkAssignDto.permissionIds),
        departmentId: bulkAssignDto.departmentId || null,
        teamId: bulkAssignDto.teamId || null,
      },
    });

    // Filter out permissions that are already assigned
    const existingPermissionIds = existingAssignments.map(a => a.permissionId);
    const newPermissionIds = bulkAssignDto.permissionIds.filter(id => !existingPermissionIds.includes(id));

    if (newPermissionIds.length === 0) {
      throw new ConflictException('All permissions are already assigned to this role for this context');
    }

    // Create new assignments
    const assignments = newPermissionIds.map(permissionId =>
      this.rolePermissionAssignmentRepository.create({
        roleId: bulkAssignDto.roleId,
        permissionId,
        departmentId: bulkAssignDto.departmentId,
        teamId: bulkAssignDto.teamId,
        createdBy,
      })
    );

    return await this.rolePermissionAssignmentRepository.save(assignments);
  }

  async findAll(paginationDto: PaginationDto, filters?: {
    roleId?: string;
    permissionId?: string;
    departmentId?: string;
    teamId?: string;
  }): Promise<PaginatedResult<RolePermissionAssignment>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.rolePermissionAssignmentRepository.createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.role', 'role')
      .leftJoinAndSelect('assignment.permission', 'permission')
      .leftJoinAndSelect('assignment.department', 'department')
      .leftJoinAndSelect('assignment.team', 'team');
    
    // Apply filters
    if (filters?.roleId) {
      queryBuilder.andWhere('assignment.roleId = :roleId', { roleId: filters.roleId });
    }
    if (filters?.permissionId) {
      queryBuilder.andWhere('assignment.permissionId = :permissionId', { permissionId: filters.permissionId });
    }
    if (filters?.departmentId) {
      queryBuilder.andWhere('assignment.departmentId = :departmentId', { departmentId: filters.departmentId });
    }
    if (filters?.teamId) {
      queryBuilder.andWhere('assignment.teamId = :teamId', { teamId: filters.teamId });
    }

    // Add search functionality
    if (search) {
      queryBuilder.andWhere(
        '(role.name LIKE :search OR role.displayName LIKE :search OR permission.action LIKE :search OR permission.description LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`assignment.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [assignments, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(assignments, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<RolePermissionAssignment> {
    const assignment = await this.rolePermissionAssignmentRepository.findOne({
      where: { id },
      relations: ['role', 'permission', 'department', 'team'],
    });

    if (!assignment) {
      throw new NotFoundException(`Role-permission assignment with ID ${id} not found`);
    }

    return assignment;
  }

  async update(id: string, updateDto: UpdateRolePermissionAssignmentDto, updatedBy?: string): Promise<RolePermissionAssignment> {
    const assignment = await this.findOne(id);

    // Validate department if being updated
    if (updateDto.departmentId && updateDto.departmentId !== assignment.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: updateDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team if being updated
    if (updateDto.teamId && updateDto.teamId !== assignment.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: updateDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }
    }

    // Update assignment
    Object.assign(assignment, updateDto, { updatedBy });
    return await this.rolePermissionAssignmentRepository.save(assignment);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const assignment = await this.findOne(id);
    
    // Soft delete
    assignment.deletedBy = deletedBy;
    await this.rolePermissionAssignmentRepository.softRemove(assignment);
  }

  async removeByRoleAndPermissions(roleId: string, permissionIds: string[], departmentId?: string, teamId?: string): Promise<void> {
    await this.rolePermissionAssignmentRepository.delete({
      roleId,
      permissionId: In(permissionIds),
      departmentId: departmentId || null,
      teamId: teamId || null,
    });
  }

  async getRolePermissions(roleId: string, departmentId?: string, teamId?: string): Promise<Permission[]> {
    const queryBuilder = this.rolePermissionAssignmentRepository.createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.permission', 'permission')
      .where('assignment.roleId = :roleId', { roleId });

    if (departmentId) {
      queryBuilder.andWhere('assignment.departmentId = :departmentId', { departmentId });
    }
    if (teamId) {
      queryBuilder.andWhere('assignment.teamId = :teamId', { teamId });
    }

    const assignments = await queryBuilder.getMany();
    return assignments.map(assignment => assignment.permission);
  }
}
