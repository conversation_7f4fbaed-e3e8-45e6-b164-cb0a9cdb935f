import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  Api<PERSON>earerAuth,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { RolePermissionAssignmentsService } from './role-permission-assignments.service';
import { CreateRolePermissionAssignmentDto } from './dto/create-role-permission-assignment.dto';
import { UpdateRolePermissionAssignmentDto } from './dto/update-role-permission-assignment.dto';
import { BulkAssignPermissionsDto } from './dto/bulk-assign-permissions.dto';
import { RolePermissionAssignmentResponseDto } from './dto/role-permission-assignment-response.dto';
import { PaginationDto, PaginatedResult } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { Permissions } from '../../common/decorators/permissions.decorator';
import { RoleType, PermissionsType } from '../../common/constants/permissions.type';

@ApiTags('Role Permission Assignments')
@Controller('role-permission-assignments')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class RolePermissionAssignmentsController {
  constructor(private readonly rolePermissionAssignmentsService: RolePermissionAssignmentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new role-permission assignment' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Assignment created successfully',
    type: RolePermissionAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Role, permission, department, or team not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Assignment already exists for this context',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  async create(@Body() createDto: CreateRolePermissionAssignmentDto, @Request() req: any): Promise<RolePermissionAssignmentResponseDto> {
    const assignment = await this.rolePermissionAssignmentsService.create(createDto, req.user.id, req.user.id);
    return plainToClass(RolePermissionAssignmentResponseDto, assignment, { excludeExtraneousValues: true });
  }

  @Post('bulk-assign')
  @ApiOperation({ summary: 'Bulk assign permissions to a role' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Permissions assigned successfully',
    type: [RolePermissionAssignmentResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Role, permissions, department, or team not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'All permissions are already assigned',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  async bulkAssign(@Body() bulkAssignDto: BulkAssignPermissionsDto, @Request() req: any): Promise<RolePermissionAssignmentResponseDto[]> {
    const assignments = await this.rolePermissionAssignmentsService.bulkAssign(bulkAssignDto, req.user.id, req.user.id);
    return assignments.map(assignment =>
      plainToClass(RolePermissionAssignmentResponseDto, assignment, { excludeExtraneousValues: true })
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all role-permission assignments with pagination and filters' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignments retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @ApiQuery({ name: 'roleId', required: false, type: String, description: 'Filter by role ID' })
  @ApiQuery({ name: 'permissionId', required: false, type: String, description: 'Filter by permission ID' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Filter by team ID' })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findAll(
    @Query() paginationDto: PaginationDto,
    @Query('roleId') roleId?: string,
    @Query('permissionId') permissionId?: string,
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ): Promise<PaginatedResult<RolePermissionAssignmentResponseDto>> {
    const filters = { roleId, permissionId, departmentId, teamId };
    const result = await this.rolePermissionAssignmentsService.findAll(paginationDto, filters);
    
    return {
      ...result,
      data: result.data.map(assignment => 
        plainToClass(RolePermissionAssignmentResponseDto, assignment, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('roles/:roleId/permissions')
  @ApiOperation({ summary: 'Get permissions assigned to a role' })
  @ApiParam({ name: 'roleId', description: 'Role ID', type: 'string' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Filter by team ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Role permissions retrieved successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async getRolePermissions(
    @Param('roleId', ParseUUIDPipe) roleId: string,
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ) {
    return await this.rolePermissionAssignmentsService.getRolePermissions(roleId, departmentId, teamId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get role-permission assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment retrieved successfully',
    type: RolePermissionAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<RolePermissionAssignmentResponseDto> {
    const assignment = await this.rolePermissionAssignmentsService.findOne(id);
    return plainToClass(RolePermissionAssignmentResponseDto, assignment, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update role-permission assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment updated successfully',
    type: RolePermissionAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Department or team not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateRolePermissionAssignmentDto,
  ): Promise<RolePermissionAssignmentResponseDto> {
    const assignment = await this.rolePermissionAssignmentsService.update(id, updateDto, 'system');
    return plainToClass(RolePermissionAssignmentResponseDto, assignment, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete role-permission assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Assignment deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.rolePermissionAssignmentsService.remove(id, 'system');
  }

  @Delete('roles/:roleId/permissions')
  @ApiOperation({ summary: 'Remove permissions from a role' })
  @ApiParam({ name: 'roleId', description: 'Role ID', type: 'string' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Department context' })
  @ApiQuery({ name: 'teamId', required: false, type: String, description: 'Team context' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Permissions removed successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  async removeRolePermissions(
    @Param('roleId', ParseUUIDPipe) roleId: string,
    @Body() body: { permissionIds: string[] },
    @Query('departmentId') departmentId?: string,
    @Query('teamId') teamId?: string,
  ): Promise<void> {
    await this.rolePermissionAssignmentsService.removeByRoleAndPermissions(
      roleId,
      body.permissionIds,
      departmentId,
      teamId
    );
  }
}
