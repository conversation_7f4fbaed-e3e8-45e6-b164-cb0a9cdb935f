import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission } from '../../entities/permission.entity';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '../../common/dto/pagination.dto';
import { PermissionsType } from '@/common/constants/permissions.type';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Permission>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.permissionRepository.createQueryBuilder('permission');
    
    // Add search functionality
    if (search) {
      queryBuilder.where(
        '(permission.action LIKE :search OR permission.description LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`permission.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [permissions, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(permissions, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Permission> {
    const permission = await this.permissionRepository.findOne({
      where: { id },
      relations: ['rolePermissions', 'permissionGroups'],
    });

    if (!permission) {
      throw new NotFoundException(`Permission with ID ${id} not found`);
    }

    return permission;
  }

  async findByAction(action: PermissionsType): Promise<Permission | null> {
    return await this.permissionRepository.findOne({
      where: { action },
    });
  }
}
