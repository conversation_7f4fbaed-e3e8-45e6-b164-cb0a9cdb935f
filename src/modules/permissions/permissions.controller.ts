import {
  <PERSON>,
  Get,
  Param,
  Query,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PermissionsService } from './permissions.service';
import { PaginationDto, PaginatedResult } from '../../common/dto/pagination.dto';
import { Permission } from '../../entities/permission.entity';
import { Roles } from '@/common/decorators/roles.decorator';
import { PermissionsType, RoleType } from '@/common/constants/permissions.type';
import { Permissions } from '@/common/decorators/permissions.decorator';

@ApiTags('Permissions')
@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all permissions with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permissions retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<Permission>> {
    return await this.permissionsService.findAll(paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get permission by ID' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Permission> {
    return await this.permissionsService.findOne(id);
  }
}
