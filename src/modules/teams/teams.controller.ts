import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { TeamsService } from './teams.service';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { TeamResponseDto } from './dto/team-response.dto';
import { PaginationDto, PaginatedResult } from '../../common/dto/pagination.dto';

@ApiTags('Teams')
@Controller('teams')
export class TeamsController {
  constructor(private readonly teamsService: TeamsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new team' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Team created successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Team name already exists in this department',
  })
  async create(@Body() createTeamDto: CreateTeamDto): Promise<TeamResponseDto> {
    const team = await this.teamsService.create(createTeamDto, 'system');
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Get()
  @ApiOperation({ summary: 'Get all teams with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Teams retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  async findAll(
    @Query() paginationDto: PaginationDto,
    @Query('departmentId') departmentId?: string,
  ): Promise<PaginatedResult<TeamResponseDto>> {
    const result = await this.teamsService.findAll(paginationDto, departmentId);
    
    return {
      ...result,
      data: result.data.map(team => 
        plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('by-department/:departmentId')
  @ApiOperation({ summary: 'Get teams by department ID' })
  @ApiParam({ name: 'departmentId', description: 'Department ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Teams retrieved successfully',
    type: [TeamResponseDto],
  })
  async findByDepartment(@Param('departmentId', ParseUUIDPipe) departmentId: string): Promise<TeamResponseDto[]> {
    const teams = await this.teamsService.findByDepartment(departmentId);
    return teams.map(team => 
      plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true })
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get team by ID' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team retrieved successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<TeamResponseDto> {
    const team = await this.teamsService.findOne(id);
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get team statistics' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team statistics retrieved successfully',
  })
  async getStats(@Param('id', ParseUUIDPipe) id: string) {
    return await this.teamsService.getTeamStats(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update team by ID' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team updated successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Team name already exists in this department',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTeamDto: UpdateTeamDto,
  ): Promise<TeamResponseDto> {
    const team = await this.teamsService.update(id, updateTeamDto, 'system');
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete team by ID' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Team deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Cannot delete team with existing users',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.teamsService.remove(id, 'system');
  }
}
