import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Team } from '../../entities/team.entity';
import { Department } from '../../entities/department.entity';
import { TeamsService } from './teams.service';
import { TeamsController } from './teams.controller';
import { CommonServicesModule } from '../../common/services/common-services.module';

@Module({
  imports: [TypeOrmModule.forFeature([Team, Department])],
  controllers: [TeamsController],
  providers: [TeamsService],
  exports: [TeamsService],
})
export class TeamsModule {}
