import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionGroup } from '../../entities/permission-group.entity';
import { Permission } from '../../entities/permission.entity';
import { PermissionGroupDetail } from '../../entities/permission-group-detail.entity';
import { PermissionGroupsService } from './permission-groups.service';
import { PermissionGroupsController } from './permission-groups.controller';

@Module({
  imports: [TypeOrmModule.forFeature([PermissionGroup, Permission, PermissionGroupDetail])],
  controllers: [PermissionGroupsController],
  providers: [PermissionGroupsService],
  exports: [PermissionGroupsService],
})
export class PermissionGroupsModule {}
