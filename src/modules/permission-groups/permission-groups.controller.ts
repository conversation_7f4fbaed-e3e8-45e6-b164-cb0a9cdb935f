import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { PermissionGroupsService } from './permission-groups.service';
import { CreatePermissionGroupDto } from './dto/create-permission-group.dto';
import { UpdatePermissionGroupDto } from './dto/update-permission-group.dto';
import { PermissionGroupResponseDto } from './dto/permission-group-response.dto';
import { PaginationDto, PaginatedResult } from '../../common/dto/pagination.dto';
import { PermissionsType, RoleType } from '@/common/constants/permissions.type';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';

class AssignPermissionsDto {
  permissionIds: string[];
}

@ApiTags('Permission Groups')
@Controller('permission-groups')
export class PermissionGroupsController {
  constructor(private readonly permissionGroupsService: PermissionGroupsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new permission group' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Permission group created successfully',
    type: PermissionGroupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Permission group name already exists',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'One or more permissions not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.CREATE)  
  async create(@Body() createPermissionGroupDto: CreatePermissionGroupDto): Promise<PermissionGroupResponseDto> {
    const permissionGroup = await this.permissionGroupsService.create(createPermissionGroupDto, 'system');
    return plainToClass(PermissionGroupResponseDto, permissionGroup, { excludeExtraneousValues: true });
  }

  @Get()
  @ApiOperation({ summary: 'Get all permission groups with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission groups retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<PermissionGroupResponseDto>> {
    const result = await this.permissionGroupsService.findAll(paginationDto);
    
    return {
      ...result,
      data: result.data.map(permissionGroup => 
        plainToClass(PermissionGroupResponseDto, permissionGroup, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get permission group by ID' })
  @ApiParam({ name: 'id', description: 'Permission Group ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission group retrieved successfully',
    type: PermissionGroupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission group not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<PermissionGroupResponseDto> {
    const permissionGroup = await this.permissionGroupsService.findOne(id);
    return plainToClass(PermissionGroupResponseDto, permissionGroup, { excludeExtraneousValues: true });
  }

  @Get(':id/permissions')
  @ApiOperation({ summary: 'Get permissions in a group' })
  @ApiParam({ name: 'id', description: 'Permission Group ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Group permissions retrieved successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async getGroupPermissions(@Param('id', ParseUUIDPipe) id: string) {
    return await this.permissionGroupsService.getGroupPermissions(id);
  }

  @Post(':id/permissions')
  @ApiOperation({ summary: 'Assign permissions to a group' })
  @ApiParam({ name: 'id', description: 'Permission Group ID', type: 'string' })
  @ApiBody({ type: AssignPermissionsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permissions assigned successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'One or more permissions not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  async assignPermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignPermissionsDto: AssignPermissionsDto,
  ): Promise<void> {
    await this.permissionGroupsService.assignPermissions(id, assignPermissionsDto.permissionIds, 'system');
  }

  @Delete(':id/permissions')
  @ApiOperation({ summary: 'Remove permissions from a group' })
  @ApiParam({ name: 'id', description: 'Permission Group ID', type: 'string' })
  @ApiBody({ type: AssignPermissionsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permissions removed successfully',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  async removePermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removePermissionsDto: AssignPermissionsDto,
  ): Promise<void> {
    await this.permissionGroupsService.removePermissions(id, removePermissionsDto.permissionIds);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update permission group by ID' })
  @ApiParam({ name: 'id', description: 'Permission Group ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission group updated successfully',
    type: PermissionGroupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission group not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Permission group name already exists',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePermissionGroupDto: UpdatePermissionGroupDto,
  ): Promise<PermissionGroupResponseDto> {
    const permissionGroup = await this.permissionGroupsService.update(id, updatePermissionGroupDto, 'system');
    return plainToClass(PermissionGroupResponseDto, permissionGroup, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete permission group by ID' })
  @ApiParam({ name: 'id', description: 'Permission Group ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Permission group deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission group not found',
  })
  @ApiBearerAuth()
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.permissionGroupsService.remove(id, 'system');
  }
}
