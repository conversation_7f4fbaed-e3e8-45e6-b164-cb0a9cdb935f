import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserRoleAssignment } from '../../entities/user-role-assignment.entity';
import { User } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';
import { Department } from '../../entities/department.entity';
import { Team } from '../../entities/team.entity';
import { UserRoleAssignmentsService } from './user-role-assignments.service';
import { UserRoleAssignmentsController } from './user-role-assignments.controller';

@Module({
  imports: [TypeOrmModule.forFeature([UserRoleAssignment, User, Role, Department, Team])],
  controllers: [UserRoleAssignmentsController],
  providers: [UserRoleAssignmentsService],
  exports: [UserRoleAssignmentsService],
})
export class UserRoleAssignmentsModule {}
