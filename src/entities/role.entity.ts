import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserRoleAssignment } from './user-role-assignment.entity';
import { RolePermissionAssignment } from './role-permission-assignment.entity';

/**
 * Role Entity
 * Represents roles in the system
 */
@Entity('roles')
export class Role extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Unique name of the role',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Display name of the role',
  })
  displayName: string;

  @Column({
    type: 'int',
    comment: 'Level of the role (1 = highest, 4 = lowest)',
  })
  level: number;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the role',
  })
  description?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a system role (cannot be deleted)',
  })
  isSystem: boolean;

  // Relationships
  @OneToMany(() => UserRoleAssignment, (userRole) => userRole.role)
  userRoles: UserRoleAssignment[];

  @OneToMany(() => RolePermissionAssignment, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermissionAssignment[];
}
