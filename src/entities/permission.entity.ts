import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { RolePermissionAssignment } from './role-permission-assignment.entity';
import { PermissionGroupDetail } from './permission-group-detail.entity';

/**
 * Permission Entity
 * Represents permissions in the system
 */
@Entity('permissions')
export class Permission extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Action name of the permission',
  })
  action: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the permission',
  })
  description?: string;

  // Relationships
  @OneToMany(() => RolePermissionAssignment, (rolePermission) => rolePermission.permission)
  rolePermissions: RolePermissionAssignment[];

  @OneToMany(() => PermissionGroupDetail, (groupDetail) => groupDetail.permission)
  permissionGroups: PermissionGroupDetail[];
}
