# 📚 Swagger API Documentation Guide

## 🌐 T<PERSON>y cập Swagger UI
**URL**: http://localhost:3000/api-docs

## ✅ Vấn đề đã được sửa!
- **Trước đây**: Swagger gọi `/auth/login` (thiếu prefix)
- **Bây giờ**: Swagger gọi `/api/v1/auth/login` (đúng prefix)

## 🔐 Hướng dẫn sử dụng Authentication

### 1. **Đăng nhập để lấy JWT Token**
- **Endpoint**: `POST /api/v1/auth/login`
- **Thông tin đăng nhập**:
  ```json
  {
    "username": "super_admin",
    "password": "aqSj0isBIqKIbkR"
  }
  ```
- **Response sẽ trả về**:
  ```json
  {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "f46a69ca-b7ab-4d28-8911-3944ba2be60c",
      "email": "<EMAIL>",
      "username": "super_admin",
      "fullName": "Super Admin",
      "roles": ["super_admin"],
      "permissions": ["update","create","print","view","import","analytics","export","delete"]
    }
  }
  ```

### 2. **Sử dụng JWT Token trong Swagger**
1. Copy `access_token` từ response login
2. Click nút **"Authorize"** ở đầu trang Swagger
3. Nhập token theo format: `Bearer YOUR_ACCESS_TOKEN`
   ```
   Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```
4. Click **"Authorize"**
5. Bây giờ bạn có thể gọi các protected endpoints

## 📋 Các API Groups chính

### 🔐 **Authentication**
- `POST /api/v1/auth/login` - Đăng nhập
- `POST /api/v1/auth/register` - Đăng ký
- `GET /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/profile` - Lấy thông tin profile

### 👥 **Users Management**
- `GET /api/v1/users` - Danh sách users
- `POST /api/v1/users` - Tạo user mới
- `GET /api/v1/users/{id}` - Chi tiết user
- `PATCH /api/v1/users/{id}` - Cập nhật user
- `DELETE /api/v1/users/{id}` - Xóa user

### 🏢 **Departments Management**
- `GET /api/v1/departments` - Danh sách departments
- `POST /api/v1/departments` - Tạo department
- `GET /api/v1/departments/{id}` - Chi tiết department
- `GET /api/v1/departments/{id}/stats` - Thống kê department

### 👨‍💼 **Teams Management**
- `GET /api/v1/teams` - Danh sách teams
- `POST /api/v1/teams` - Tạo team
- `GET /api/v1/teams/by-department/{departmentId}` - Teams theo department

### 🔑 **Permissions & Roles**
- `GET /api/v1/permissions` - Danh sách permissions
- `GET /api/v1/roles` - Danh sách roles
- `GET /api/v1/roles/system` - System roles
- `GET /api/v1/roles/non-system` - Non-system roles

### 📦 **Permission Groups**
- `GET /api/v1/permission-groups` - Danh sách permission groups
- `POST /api/v1/permission-groups` - Tạo permission group
- `POST /api/v1/permission-groups/{id}/permissions` - Gán permissions cho group

### 🔗 **Role-Permission Assignments**
- `POST /api/v1/role-permission-assignments` - Gán permission cho role
- `POST /api/v1/role-permission-assignments/bulk-assign` - Gán nhiều permissions
- `GET /api/v1/role-permission-assignments/roles/{roleId}/permissions` - Xem permissions của role

### 👤 **User-Role Assignments**
- `POST /api/v1/user-role-assignments` - Gán role cho user
- `POST /api/v1/user-role-assignments/bulk-assign` - Gán nhiều roles
- `GET /api/v1/user-role-assignments/users/{userId}/roles` - Xem roles của user

## 🧪 Test Flow trong Swagger

### **Bước 1: Authentication**
1. Gọi `POST /api/v1/auth/login` với thông tin super_admin
2. Copy access_token từ response
3. Click "Authorize" và nhập token

### **Bước 2: Tạo Department**
1. Gọi `POST /api/v1/departments`
2. Body: `{"name": "IT Department"}`

### **Bước 3: Tạo Team**
1. Gọi `POST /api/v1/teams`
2. Body: `{"name": "Backend Team", "departmentId": "DEPARTMENT_ID_FROM_STEP_2"}`

### **Bước 4: Tạo User**
1. Gọi `POST /api/v1/users`
2. Body:
   ```json
   {
     "username": "developer1",
     "fullName": "John Developer",
     "email": "<EMAIL>",
     "password": "SecurePass123!"
   }
   ```

### **Bước 5: Gán Role cho User**
1. Gọi `POST /api/v1/user-role-assignments`
2. Body:
   ```json
   {
     "userId": "USER_ID_FROM_STEP_4",
     "roleId": "ROLE_ID_FROM_GET_ROLES",
     "departmentId": "DEPARTMENT_ID_FROM_STEP_2",
     "teamId": "TEAM_ID_FROM_STEP_3"
   }
   ```

## 🎯 Tính năng Swagger UI

### ✅ **Đã hoạt động hoàn hảo:**
- **Correct API Prefix**: Tất cả endpoints đều có prefix `/api/v1`
- **JWT Authentication**: Bearer token authentication
- **Try it out**: Test trực tiếp từ UI
- **Request/Response Examples**: Ví dụ đầy đủ
- **Validation**: Input validation hiển thị rõ ràng
- **Error Handling**: Error responses được document đầy đủ

## 🚀 **Swagger UI đã sẵn sàng sử dụng!**
Bạn có thể test toàn bộ RBAC system trực tiếp từ Swagger interface một cách dễ dàng và trực quan.
